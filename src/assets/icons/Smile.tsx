type IconProps = React.SVGProps<SVGSVGElement>;

export function Smile(props: IconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="32"
      height="33"
      fill="none"
      viewBox="0 0 32 33"
      {...props}
    >
      <path
        fill="#1A181E"
        d="m11.433 10.431 2.648 3.667c.117-.306.235-.673.411-.978-1.53.428-3.117.428-4.706.122a.72.72 0 0 0-.823.49c-.059.305.117.732.47.855 1.765.366 3.589.366 5.353-.123.353-.122.647-.61.412-.977L12.551 9.82c-.235-.306-.53-.428-.882-.244-.294.122-.47.55-.236.855M23.433 12.081c-1.647.55-3.352 1.1-5 1.589-.411.122-.588.611-.411.978a12.2 12.2 0 0 0 3.059 4.278c.588.61 1.529-.367.94-.978-1.176-1.161-2.176-2.506-2.823-4.034-.117.306-.235.673-.412.978 1.647-.55 3.353-1.1 5-1.589.765-.183.412-1.466-.353-1.222M6.845 17.337c-.353.916.53 2.26 1 2.933.883 1.467 2.118 2.811 3.588 3.728 2.765 1.65 6.706 2.383 9.53.489.706-.49 1.47-1.161 1.764-2.078.236-.795-1-1.161-1.235-.367 0-.06-.059.122-.176.245-.059.06-.177.244-.294.366-.353.367-.706.673-1.177.856-1.47.794-3.235.794-4.764.428-2.118-.49-3.942-1.528-5.353-3.24-.53-.61-.942-1.344-1.295-2.077-.117-.245-.235-.489-.294-.733-.059-.123-.059-.245-.059-.306v.061c.295-.794-.94-1.1-1.235-.305"
      ></path>
      <path
        fill="#1A181E"
        d="M12.492 2.731C6.904 4.32 2.14 9.27 1.433 15.442c-.353 2.933.236 6.05 1.647 8.678 1.589 2.872 4.177 4.889 7.177 5.989 3.176 1.161 6.882 1.589 10.118.428 2.647-.978 4.882-2.873 6.529-5.256 3.47-5.133 3.177-11.794-.353-16.805-1.882-2.69-4.53-4.89-7.588-5.99-2.941-.977-6.647-.977-8.765 1.528-.53.673.353 1.59.941.978 1.765-2.078 4.647-2.078 7-1.344 2.53.794 4.765 2.383 6.47 4.46 3.471 4.095 4.59 9.962 2.118 14.85-1.176 2.384-3 4.4-5.294 5.623-2.764 1.528-5.94 1.528-8.94.794-2.824-.672-5.471-2.016-7.295-4.46-1.647-2.14-2.47-4.828-2.53-7.517C2.493 11.042 7.14 5.664 12.846 4.014c.824-.244.47-1.527-.353-1.283"
      ></path>
      <path
        fill="#1A181E"
        d="M13.963 24.548a6 6 0 0 0 0 2.505c.176.856.53 1.65 1.176 2.2.706.55 1.647.55 2.412.184.765-.428 1.235-1.162 1.53-1.956q.528-1.283.352-2.75c-.058-.367-.47-.55-.823-.489-.353.122-.47.49-.47.856-.06-.245 0 0 0 .06v.245c0 .184 0 .367-.06.49v.06c0 .061 0 .123-.058.245q-.09.274-.177.55c0 .06-.059.183-.059.244s-.059.122-.059.122c-.058.123-.176.306-.235.428-.059.061-.117.122-.117.183l-.06.062-.176.183-.117.122q.088-.09 0 0c-.06.061-.118.061-.177.122-.059.062-.176.062-.059.062-.059 0-.117.06-.176.06 0 0-.118.062-.118 0h-.235.059-.118c-.059 0-.235-.122-.117 0-.06-.06-.118-.06-.177-.122-.059 0-.059-.06-.118-.06 0 0 .118.122.06 0l-.177-.184-.06-.061c-.058-.061.06.122 0 0a.47.47 0 0 1-.117-.245c-.059-.06-.059-.122-.117-.183-.06-.122 0-.061 0 0 0-.061 0-.061-.06-.122l-.176-.55c0-.122-.058-.184-.058-.306v-.122c0-.183-.06-.428-.06-.611v-.672.06-.06c0-.061.06-.184.06-.245.058-.366-.118-.733-.471-.855-.177 0-.53.183-.647.55"
      ></path>
    </svg>
  );
}
