"use client";
import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogOverlay,
  DialogPortal,
} from "@/components/ui/dialog";
import { X, Plus, Minus } from "lucide-react";
import { DeliveryInfoProps } from "@/components/Partials/PDP/types";
import Image from "next/image";

const DeliveryInfoModal: React.FC<DeliveryInfoProps> = () => {
  const [isOpen, setIsOpen] = useState(true);
  const [pincodeInput, setPincodeInput] = useState("");
  const [hasPincode, setHasPincode] = useState(false);
  const [error, setError] = useState("");
  const [isAccordionOpen, setIsAccordionOpen] = useState(false);
  const validPincodes = ["394190", "110001", "400001", "560001", "600001"];

  const handleClose = () => setIsOpen(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "");
    if (value.length <= 6) {
      setPincodeInput(value);
      setError("");
    }
  };

  const handleCheckPincode = () => {
    if (pincodeInput.length === 6) {
      if (validPincodes.includes(pincodeInput)) {
        setHasPincode(true);
        setError("");
      } else {
        setHasPincode(false);
        setError("Sorry, delivery is not available for this pincode.");
      }
    }
  };

  const handleReset = () => {
    setPincodeInput("");
    setHasPincode(false);
    setError("");
  };

  // Added missing toggleAccordion function
  const toggleAccordion = () => {
    setIsAccordionOpen(!isAccordionOpen);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogPortal>
        <DialogOverlay className="!z-[9998]" />
        <DialogContent
          className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[315px] rounded-[10px] border border-[#67263e] bg-[#fff3f1] py-6 px-6 transition-all duration-300 ease-in-out !z-[9999] [&>button:not([data-custom-close])]:hidden"
          style={{
            boxShadow: "-6px 6px 0px 0px rgba(147, 56, 93)",
          }}
        >
          <DialogClose
            data-custom-close
            className="absolute -top-[14px] -right-[14px] w-[33px] h-[33px] border border-[#6f1d46] bg-[#6f1d46] rounded-full flex items-center justify-center cursor-pointer focus:outline-none !z-[60]"
            style={{
              boxShadow: "-3px 3px 0px 0px rgba(147, 56, 93, 0.5)",
              borderWidth: "0.9px",
            }}
          >
            <X className="h-4 w-4 text-white" />
            <span className="sr-only">Close</span>
          </DialogClose>

          {/* Delivery Checker */}
          <div className="w-[263px] rounded-[6px] border border-[#67263e] bg-[#ffe6ef] p-3">
            <div className="flex items-center gap-1">
              <Image
                src={"/images/partials/locationIcon.png"}
                width={8}
                height={11}
                alt="Location"
              />
              <p className="font-obviously text-[10px] leading-4 font-semibold text-black m-0">
                {hasPincode ? (
                  <span className="font-medium">
                    Delivery Options for{" "}
                    <span className="font-semibold ">{pincodeInput}</span>
                  </span>
                ) : (
                  "Enter Delivery Pincode"
                )}
              </p>
              {hasPincode && (
                <button
                  onClick={handleReset}
                  className="text-[#77274F] ml-2 underline leading-2 text-[8px] font-obviously cursor-pointer font-medium"
                >
                  Change
                </button>
              )}
            </div>

            {!hasPincode ? (
              <div className="flex items-center gap-1 bg-[#FFE6EF]">
                <input
                  type="text"
                  inputMode="numeric"
                  name="pin"
                  value={pincodeInput}
                  onChange={handleInputChange}
                  maxLength={6}
                  placeholder="Enter pincode"
                  className="w-[150px] h-[28px] rounded border-[0.1px] border-black/20 bg-white px-[11px] py-[10px] font-obviously font-medium text-[9px] leading-[14px] focus:outline-none focus:ring-0"
                />
                <button
                  onClick={handleCheckPincode}
                  disabled={pincodeInput.length !== 6}
                  className="w-[80px] h-[29px] border-none rounded bg-[#93385d] flex justify-center items-center font-obviously font-semibold text-[12px] leading-[19px] text-white text-center disabled:opacity-60 disabled:cursor-not-allowed"
                  style={{
                    backgroundColor:
                      pincodeInput.length === 6 ? "#93385d" : "#93385d66",
                  }}
                >
                  Check
                </button>
              </div>
            ) : (
              <div className="my-3">
                <p className="text-[10px] font-obviously leading-4 text-black">
                  Yay! Your pincode is eligible for delivery.
                </p>
                <p className="text-[10px] font-obviously leading-4 text-[#77274F]">
                  Delivery by{" "}
                  <span className="text-sm font-semibold text-[#6F1D46]">
                    Saturday, 5 June
                  </span>
                </p>
              </div>
            )}

            {error && (
              <p className="text-[10px] font-medium text-red-500 mt-1 font-obviously">
                {error}
              </p>
            )}
          </div>

          <div>
            <div className="w-full bg-transparent">
              {/* Accordion Header */}
              <button
                onClick={toggleAccordion}
                className="w-full flex items-center justify-between p-2  transition-colors duration-200 border-y border-gray-200"
                aria-expanded={isAccordionOpen}
              >
                <h2 className="text-base font-narrow font-semibold text-gray-900">
                  Same-Day Delivery (SDD) Rules
                </h2>
                <div className="ml-4 flex-shrink-0">
                  {isAccordionOpen ? (
                    <Minus className="w-5 h-5 text-black" />
                  ) : (
                    <Plus className="w-5 h-5 text-black" />
                  )}
                </div>
              </button>

              {/* Accordion Content */}
              <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  isAccordionOpen
                    ? "max-h-screen opacity-100"
                    : "max-h-0 opacity-0"
                }`}
              >
                <div className="p-2 bg-transparent space-y-4">
                  {/* Available Pincodes */}
                  <div className="font-obviously leading-3.5">
                    <span className="font-[560]  text-[9px] mb-2">
                      Available Pincodes:
                    </span>
                    <span className="font-normal text-[9px]">
                      {" "}
                      SDD is available in select locations. Check your pincode
                      to see if it's available for your address.
                    </span>
                  </div>
                  <div className="font-obviously leading-3.5">
                    <span className="font-[560]  text-[9px] mb-2">
                      Available Pincodes:
                    </span>
                    <span className="font-normal text-[9px]">
                      {" "}
                      SDD is available in select locations. Check your pincode
                      to see if it's available for your address.
                    </span>
                  </div>
                  <div className="font-obviously leading-3.5">
                    <span className="font-[560]  text-[9px] mb-2">
                      Available Pincodes:
                    </span>
                    <span className="font-normal text-[9px]">
                      {" "}
                      SDD is available in select locations. Check your pincode
                      to see if it's available for your address.
                    </span>
                  </div>
                  <div className="font-obviously leading-3.5">
                    <span className="font-[560]  text-[9px] mb-2">
                      Available Pincodes:
                    </span>
                    <span className="font-normal text-[9px]">
                      {" "}
                      SDD is available in select locations. Check your pincode
                      to see if it's available for your address.
                    </span>
                  </div>
                  <div className="font-obviously leading-3.5">
                    <span className="font-[560]  text-[9px] mb-2">
                      Available Pincodes:
                    </span>
                    <span className="font-normal text-[9px]">
                      {" "}
                      SDD is available in select locations. Check your pincode
                      to see if it's available for your address.
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

export default DeliveryInfoModal;
