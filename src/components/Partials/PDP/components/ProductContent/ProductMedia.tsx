import React, { useState, useEffect } from "react";
import ProductCarouselSection from "../../ProductCarouselSection";
import ProductRatingSection from "../../ProductRatingSection";
import { ProductMediaProps } from "../../types";
import { useProductContext } from "../../context/ProductContext";

/**
 * ProductMedia Component
 *
 * Contains product carousel and rating section
 * Automatically switches to variant-specific images when variants change
 */
export const ProductMedia: React.FC<ProductMediaProps> = ({
  strapiProduct,
  medusaProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const primaryColor = strapiProduct?.primary_color || "#036A38";

  // Get combined images and preferred index from context
  const { combinedImages, preferredImageIndex, setPreferredImageIndex } =
    useProductContext();

  console.log("combinedImages: ", combinedImages);

  // Update current slide when preferred index changes (variant switch)
  useEffect(() => {
    setCurrentSlide(preferredImageIndex);
  }, [preferredImageIndex]);

  // Update context when user manually changes slide
  const handleSlideChange = (index: number) => {
    setCurrentSlide(index);
    setPreferredImageIndex(index);
  };

  return (
    <>
      {/* Product Carousel */}
      <ProductCarouselSection
        images={combinedImages}
        title={medusaProduct?.title || "Product"}
        currentSlide={currentSlide}
        onSlideChange={handleSlideChange}
        primaryColor={primaryColor}
      />

      {/* Rating and Reviews */}
      <ProductRatingSection
        productId={strapiProduct?.systemId || ""}
        primaryColor={primaryColor}
      />
    </>
  );
};
