import React, { useState } from "react";
import ProductCarouselSection from "../../ProductCarouselSection";
import ProductRatingSection from "../../ProductRatingSection";
import { ProductMediaProps } from "../../types";
import { useProductContext } from "../../context/ProductContext";

/**
 * ProductMedia Component
 *
 * Contains product carousel and rating section
 */
export const ProductMedia: React.FC<ProductMediaProps> = ({
  strapiProduct,
  medusaProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const primaryColor = strapiProduct?.primary_color || "#036A38";

  // Get combined images from context (calculated based on active variant)
  const { combinedImages } = useProductContext();

  console.log("combinedImages: ", combinedImages);

  return (
    <>
      {/* Product Carousel */}
      <ProductCarouselSection
        images={combinedImages}
        title={medusaProduct?.title || "Product"}
        currentSlide={currentSlide}
        onSlideChange={setCurrentSlide}
        primaryColor={primaryColor}
      />

      {/* Rating and Reviews */}
      <ProductRatingSection
        productId={strapiProduct?.systemId || ""}
        primaryColor={primaryColor}
      />
    </>
  );
};
