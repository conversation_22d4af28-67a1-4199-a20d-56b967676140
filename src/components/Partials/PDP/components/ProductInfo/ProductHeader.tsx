import React, { useMemo } from "react";
import PageBreadcrumb from "@/components/Common/PageBreadcrumb";
import { ProductHeaderProps } from "../../types";

/**
 * ProductHeader Component
 *
 * Displays product breadcrumb, title, and delivery info
 */
export const ProductHeader: React.FC<ProductHeaderProps> = ({
  strapiProduct,
  medusaProduct,
}) => {
  const primaryColor = strapiProduct?.primary_color || "#036A38";

  const categoryName = medusaProduct?.categories?.[0]?.name || "";
  const categoryHandle = medusaProduct?.categories?.[0]?.handle;

  // Generate breadcrumb items
  const breadcrumbItems = useMemo(
    () => [
      { label: "Home", href: "/" },
      {
        label: categoryName,
        href: categoryHandle ? `/categories/${categoryHandle}` : "#",
      },
    ],
    [categoryName, categoryHandle]
  );

  return (
    <>
      <PageBreadcrumb
        className="px-0 mb-2 mt-6 md:mt-0"
        items={breadcrumbItems}
      />
      <h1
        className="text-2xl md:text-[40px] leading-8 md:leading-12 mb-8 font-semibold font-narrow"
        style={{ color: primaryColor }}
      >
        {medusaProduct?.title}
      </h1>
    </>
  );
};
