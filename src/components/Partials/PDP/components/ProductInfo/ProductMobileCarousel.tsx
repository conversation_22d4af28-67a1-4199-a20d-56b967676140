import React, { useState, useEffect } from "react";
import MobileProductCarousel from "../../MobileProductCarousel";
import { ProductMobileCarouselProps } from "../../types";
import { useProductContext } from "../../context/ProductContext";

/**
 * ProductMobileCarousel Component
 *
 * Wrapper for the mobile product carousel with props-based data
 * Automatically switches to variant-specific images when variants change
 */
export const ProductMobileCarousel: React.FC<ProductMobileCarouselProps> = ({
  strapiProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
  medusaProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Get combined images and preferred index from context
  const { combinedImages, preferredImageIndex, setPreferredImageIndex } =
    useProductContext();

  // Update current slide when preferred index changes (variant switch)
  useEffect(() => {
    setCurrentSlide(preferredImageIndex);
  }, [preferredImageIndex]);

  // Update context when user manually changes slide
  const handleSlideChange = (index: number) => {
    setCurrentSlide(index);
    setPreferredImageIndex(index);
  };

  return (
    <MobileProductCarousel
      images={combinedImages}
      title={medusaProduct?.title || "Product"}
      currentSlide={currentSlide}
      onSlideChange={handleSlideChange}
      primaryColor={strapiProduct?.primary_color || "#036A38"}
    />
  );
};
