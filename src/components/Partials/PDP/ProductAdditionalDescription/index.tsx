"use client";

import { Smile } from "@/assets/icons/Smile";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import React from "react";

const ProductAdditionalDescription = (props: {
  productData: ProductDetailsType;
}) => {
  // Early return if no data or description provided
  if (!props.productData.additional_description?.show_component) return null;

  return (
    <>
      <div
        className="mt-12.5 font-obviously [&>ul]:list-disc pl-4"
        dangerouslySetInnerHTML={{
          __html: props.productData.additional_description?.description,
        }}
      />
      <div className="mt-2.5 flex items-center gap-2">
        <h4 className="font-gooddog text-2xl font-medium leading-8">
          and no hidden * marks either
        </h4>
        <Smile />
      </div>
    </>
  );
};

export default ProductAdditionalDescription;
