"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
  useRef,
} from "react";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import {
  ExtendedMedusaProductWithStrapiProduct,
  ExtendedVariant,
} from "@/types/Medusa/Product";

// ============================================================================
// CONTEXT TYPES
// ============================================================================

interface ProductContextState {
  // Product data
  strapiProduct: ProductDetailsType | null;
  medusaProduct: ExtendedMedusaProductWithStrapiProduct | null;

  // UI state
  activeVariant: ExtendedVariant | null;
  quantity: number;
  preferredImageIndex: number;

  // Computed values
  combinedImages: string[];
  firstVariantImageIndex: number;
  currentPrice: number;
  originalPrice: number;
  loyaltyPoints: number;
  discountPercentage: number;
  hasDiscount: boolean;
  formattedCurrentPrice: string;
  formattedOriginalPrice: string;
}

interface ProductContextActions {
  setActiveVariant: (variant: ExtendedVariant) => void;
  setQuantity: (quantity: number) => void;
  incrementQuantity: () => void;
  decrementQuantity: () => void;
  setPreferredImageIndex: (index: number) => void;
  resetToVariantImage: () => void;
}

interface ProductContextValue
  extends ProductContextState,
    ProductContextActions {}

interface ProductProviderProps {
  children: React.ReactNode;
  strapiProduct: ProductDetailsType | null;
  medusaProduct: ExtendedMedusaProductWithStrapiProduct | null;
}

// ============================================================================
// CONTEXT CREATION
// ============================================================================

const ProductContext = createContext<ProductContextValue | undefined>(
  undefined
);

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Add S3 bucket prefix to URL if it's a relative path
 */
const formatImageUrl = (url: string): string => {
  const S3_BUCKET = process.env.NEXT_PUBLIC_S3_BUCKET || "";

  // If URL is already a full URL (starts with http/https), return as is
  if (url.startsWith("http://") || url.startsWith("https://")) {
    return url;
  }

  // If URL starts with '/', remove it to avoid double slashes
  const cleanUrl = url.startsWith("/") ? url.slice(1) : url;

  // Add S3 bucket prefix
  return S3_BUCKET ? `${S3_BUCKET}/${cleanUrl}` : url;
};

/**
 * Generate combined images from global and variant-specific sources
 * Returns both the combined images array and the index of the first variant-specific image
 */
const generateCombinedImages = (
  medusaProduct: ExtendedMedusaProductWithStrapiProduct | null,
  activeVariant: ExtendedVariant | null
): { combinedImages: string[]; firstVariantImageIndex: number } => {
  const globalImages: string[] = [];
  const variantImages: string[] = [];

  // Extract global product images
  if (medusaProduct?.images && Array.isArray(medusaProduct.images)) {
    globalImages.push(
      ...medusaProduct.images.map((img: any) => formatImageUrl(img.url || img))
    );
  }

  // Extract variant-specific images
  if (
    activeVariant?.variant_image &&
    Array.isArray(activeVariant.variant_image)
  ) {
    const sortedVariantImages = [...activeVariant.variant_image].sort(
      (a, b) => a.rank - b.rank
    );
    variantImages.push(
      ...sortedVariantImages.map((img) => formatImageUrl(img.url))
    );
  }

  // Combine global images first, then variant-specific images
  const combined = [...globalImages, ...variantImages];

  // Calculate the index where variant-specific images start
  const firstVariantImageIndex = globalImages.length;

  // Debug logging (can be removed in production)
  if (process.env.NODE_ENV === "development") {
    console.log("🖼️ Image Generation:", {
      activeVariantId: activeVariant?.id,
      globalImages: globalImages.length,
      variantImages: variantImages.length,
      firstVariantImageIndex,
      totalImages: combined.length,
    });
  }

  // Fallback to sample images if no images are available
  if (combined.length === 0) {
    const fallbackImages = [
      formatImageUrl("/images/products/pdp/image.png"),
      formatImageUrl("/images/products/badaam/highlight/2.webp"),
      formatImageUrl("/images/products/badaam/highlight/3.webp"),
      formatImageUrl("/images/products/badaam/highlight/4.webp"),
      formatImageUrl("/images/products/badaam/highlight/5.webp"),
    ];
    return {
      combinedImages: fallbackImages,
      firstVariantImageIndex: 0, // No variant images in fallback
    };
  }

  return {
    combinedImages: combined,
    firstVariantImageIndex:
      variantImages.length > 0 ? firstVariantImageIndex : -1, // Return -1 if no variant images
  };
};

/**
 * Calculate pricing information from active variant
 */
const calculatePricing = (activeVariant: ExtendedVariant | null) => {
  if (!activeVariant) {
    return {
      currentPrice: 0,
      originalPrice: 0,
      loyaltyPoints: 0,
      discountPercentage: 0,
      hasDiscount: false,
      formattedCurrentPrice: "₹0",
      formattedOriginalPrice: "₹0",
    };
  }

  const currentPrice = activeVariant.calculated_price?.calculated_amount || 0;

  const originalPrice =
    activeVariant.extended_product_variants?.compare_at_price ||
    activeVariant.calculated_price?.calculated_amount ||
    0;

  const loyaltyPoints = Math.floor(currentPrice * 0.02);

  // Calculate discount percentage
  const discountPercentage =
    originalPrice > currentPrice && originalPrice > 0
      ? Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
      : 0;

  return {
    currentPrice,
    originalPrice,
    loyaltyPoints,
    discountPercentage,
    hasDiscount: originalPrice > currentPrice && discountPercentage > 0,
    formattedCurrentPrice: `₹${currentPrice?.toLocaleString()}`,
    formattedOriginalPrice: `₹${originalPrice?.toLocaleString()}`,
  };
};

// ============================================================================
// PROVIDER COMPONENT
// ============================================================================

export const ProductProvider: React.FC<ProductProviderProps> = ({
  children,
  strapiProduct,
  medusaProduct,
}) => {
  const [activeVariant, setActiveVariant] = useState<ExtendedVariant | null>(
    null
  );
  const [quantity, setQuantity] = useState(1);
  const [preferredImageIndex, setPreferredImageIndex] = useState(0);
  const previousVariantIdRef = useRef<string | null>(null);

  // Initialize active variant when medusaProduct changes
  useEffect(() => {
    if (medusaProduct?.variants && medusaProduct.variants.length > 0) {
      setActiveVariant(medusaProduct.variants[0]);
    }
  }, [medusaProduct]);

  // Calculate computed values
  const computedValues = useMemo(() => {
    const imageData = generateCombinedImages(medusaProduct, activeVariant);
    const pricing = calculatePricing(activeVariant);

    return {
      combinedImages: imageData.combinedImages,
      firstVariantImageIndex: imageData.firstVariantImageIndex,
      ...pricing,
    };
  }, [medusaProduct, activeVariant]);

  // Actions
  const incrementQuantity = () => setQuantity((prev) => prev + 1);
  const decrementQuantity = () => setQuantity((prev) => Math.max(1, prev - 1));

  // Reset to first variant-specific image when variant changes
  const resetToVariantImage = () => {
    if (computedValues.firstVariantImageIndex >= 0) {
      setPreferredImageIndex(computedValues.firstVariantImageIndex);
    }
  };

  // Auto-reset to variant image when activeVariant changes
  useEffect(() => {
    const currentVariantId = activeVariant?.id || null;
    const hasVariantChanged = previousVariantIdRef.current !== currentVariantId;

    // Debug logging (can be removed in production)
    if (process.env.NODE_ENV === "development") {
      console.log("🔄 Variant Change:", {
        variantId: currentVariantId,
        hasChanged: hasVariantChanged,
        firstVariantImageIndex: computedValues.firstVariantImageIndex,
      });
    }

    // Only update if variant actually changed
    if (hasVariantChanged && activeVariant) {
      // If variant has specific images, jump to first variant image
      if (computedValues.firstVariantImageIndex >= 0) {
        setPreferredImageIndex(computedValues.firstVariantImageIndex);
      } else {
        // If no variant-specific images, stay at index 0 (first global image)
        setPreferredImageIndex(0);
      }

      // Update the ref to track the current variant
      previousVariantIdRef.current = currentVariantId;
    }
  }, [activeVariant, computedValues.firstVariantImageIndex]); // Include activeVariant to satisfy linter

  const contextValue: ProductContextValue = {
    // Product data
    strapiProduct,
    medusaProduct,

    // UI state
    activeVariant,
    quantity,
    preferredImageIndex,

    // Computed values
    ...computedValues,

    // Actions
    setActiveVariant,
    setQuantity,
    incrementQuantity,
    decrementQuantity,
    setPreferredImageIndex,
    resetToVariantImage,
  };

  return (
    <ProductContext.Provider value={contextValue}>
      {children}
    </ProductContext.Provider>
  );
};

// ============================================================================
// HOOK
// ============================================================================

export const useProductContext = (): ProductContextValue => {
  const context = useContext(ProductContext);
  if (context === undefined) {
    throw new Error("useProductContext must be used within a ProductProvider");
  }
  return context;
};
