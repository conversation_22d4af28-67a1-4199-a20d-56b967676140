// TODO: need to manage discounted price
// TODO: need to add product image
import { UniversalCarousel } from "@/components/Common/CarouselWrapper.tsx";
import { sdk } from "@/libs/medusaClient";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import { ExtendedMedusaProductWithStrapiProduct } from "@/types/Medusa/Product";

import Image from "next/image";
import Link from "next/link";
import React from "react";
import QuickAddIconButton from "../QuickAddIconButton";
import { calculateDiscount } from "@/utils/pricing";

const QuickAddCard = async (props: { productData: ProductDetailsType }) => {
  const medusaSystemId = props.productData.quickly_added_category?.systemId;
  console.log("medusaSystemId: ", medusaSystemId);
  // Return early if no category ID is available
  if (!medusaSystemId) {
    return null;
  }

  // need to fetch data from the medusa now
  const quicklyAddedProducts = await sdk.store.product.list({
    category_id: medusaSystemId,
    fields:
      "+variants.extended_product_variants.*,cms_product.*,+variants.prices.*,+variants.variant_image.*",
    limit: 10,
  });

  console.log("quicklyAddedProducts: ", quicklyAddedProducts);

  const extendedProducts =
    quicklyAddedProducts.products as ExtendedMedusaProductWithStrapiProduct[];

  const filterCmsData = extendedProducts.filter(
    (product) => product.cms_product
  );

  const quicklyAddedProductsData = filterCmsData.map((product) => {
    // Get the first variant (rank 0) for image

    const extendedVariants = product.variants;
    const firstVariant = extendedVariants?.find(
      (variant) => variant.variant_rank === 0
    );

    // Get primary image URL from variant_image array where rank is 0
    const primaryImageUrl =
      firstVariant?.variant_image?.find((image) => image.rank === 0)?.url ||
      null;

    // Get all variant images URLs
    const variantImages =
      extendedVariants?.map((variant) => {
        return variant.variant_image?.map((image) => image.url);
      }) || [];

    // Extract original price from variant with rank 0
    const primaryVariant = extendedVariants?.find(
      (variant) => variant.variant_rank === 0
    );

    // Get price based on your preferred currency (e.g., 'inr', 'usd', 'eur')
    // You can modify this logic based on your requirements
    // TODO: need to change as per requirement
    const originalPrice =
      primaryVariant?.prices?.find(
        (price) => price.currency_code === "inr" && price.rules_count === 0
      )?.amount ||
      primaryVariant?.prices?.find((price) => price.currency_code === "eur")
        ?.amount ||
      primaryVariant?.prices?.[0]?.amount ||
      null;

    const discountedPrice =
      primaryVariant?.extended_product_variants?.compare_at_price || null;

    return {
      title: product.title,
      primaryColor: product.cms_product.primary_color,
      bgColor: product.cms_product.bg_color,
      handle: product.cms_product.handle,
      productId: product.id,
      image: primaryImageUrl, // URL of the image with rank 0
      originalPrice: originalPrice,
      discountedPrice: discountedPrice,
      allVariantImages: variantImages.flat(),
      hasDiscount: discountedPrice !== null,
    };
  });


  return (
    <div className="text-center max-w-[1240px] mx-auto px-6">
      <p className="text-[28px] md:text-4xl text-black font-narrow font-semibold mb-8">
        Quickly Added
      </p>

      {/* card carousel */}
      <UniversalCarousel
        gapClassName="gap-6.5"
        className="py-6"
        hideScrollbar={false}
        scrollbarColor="#44426B"
        scrollBarTrackColor="#998CA5"
        useNativeScrollbar={true}
      >
        {quicklyAddedProductsData.map((product) => {
          console.log("p@@", product.image);

          return (
            <div
              key={product.productId}
              className="relative text-left text-black max-w-[224px] min-w-[200px] h-auto flex flex-col rounded-[10px] overflow-hidden shadow-[-2px_4px_8px_0px_rgba(0,0,0,0.12)]"
              style={{ borderColor: product.primaryColor, borderWidth: "1px" }}
            >
              <div className="h-[224px] w-[224px]">
                {/* Add Icon */}
                <QuickAddIconButton
                  productId={product.productId}
                  primaryColor={product.primaryColor}
                  variantId={product.productId}
                />
                {/* Product Image */}
                <Link href={`/products/${product.handle}`} className="block">
                  <div className="relative aspect-square w-full h-full">
                    <Image
                      src={"/images/placeholder.png"}
                      alt="Product Image"
                      fill
                      className="object-contain"
                    />
                  </div>
                </Link>
              </div>

              {/* card content */}
              <Link href={`/products/${product.handle}`} className="block">
                <div
                  className="py-2 px-3 flex flex-col justify-between space-y-3 h-[88px]"
                  style={{ backgroundColor: product.primaryColor }}
                >
                  {/* Product Title */}
                  <p className="line-clamp-2 text-[11.5px] font-semibold leading-4 font-obviously text-white h-8 overflow-hidden">
                    {product.title}
                  </p>
                  {/* Product Price */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5">
                      <span className="text-base font-semibold leading-6.5 font-obviously text-white">
                        ₹
                        {product.discountedPrice !== null
                          ? product.discountedPrice / 100
                          : product.originalPrice
                          ? product.originalPrice / 100
                          : 0}
                      </span>
                      {product.discountedPrice !== null &&
                        product.originalPrice !== null &&
                        product.discountedPrice !== product.originalPrice && (
                          <span className="line-through text-[15.6px] leading-6 font-normal font-obviously text-white/45">
                            ₹{(product.originalPrice ?? 0) / 100}
                          </span>
                        )}
                    </div>
                    {/* Discount */}
                    {product.discountedPrice !== null &&
                      product.originalPrice !== null &&
                      product.discountedPrice !== product.originalPrice && (
                        <span
                          className="flex items-center gap-1 text-[10.5px] font-semibold leading-[11px] font-obviously text-[#7a6856] rounded-[2.6px] p-1"
                          style={{ backgroundColor: product.bgColor }}
                        >
                          (
                          {calculateDiscount(
                            product.originalPrice,
                            product.discountedPrice
                          )}
                          % OFF)
                        </span>
                      )}
                  </div>
                </div>
              </Link>
            </div>
          );
        })}
      </UniversalCarousel>
    </div>
  );
};

export default QuickAddCard;
