// Interface automatically generated by schemas-to-ts

import { Icon } from '../../media/interfaces/Icon';
import { Icon_Plain } from '../../media/interfaces/Icon';
import { Icon_NoRelations } from '../../media/interfaces/Icon';

export interface Delivery {
  title?: string;
  image?: Icon;
  comment?: string;
}
export interface Delivery_Plain {
  title?: string;
  image?: Icon_Plain;
  comment?: string;
}

export interface Delivery_NoRelations {
  title?: string;
  image?: Icon_NoRelations;
  comment?: string;
}

