// Interface automatically generated by schemas-to-ts

import { Image } from '../../media/interfaces/Image';
import { Image_Plain } from '../../media/interfaces/Image';
import { Image_NoRelations } from '../../media/interfaces/Image';

export interface KnowWhatYouEat {
  title?: string;
  subtitle?: string;
  comment?: string;
  description?: string;
  image?: Image;
}
export interface KnowWhatYouEat_Plain {
  title?: string;
  subtitle?: string;
  comment?: string;
  description?: string;
  image?: Image_Plain;
}

export interface KnowWhatYouEat_NoRelations {
  title?: string;
  subtitle?: string;
  comment?: string;
  description?: string;
  image?: Image_NoRelations;
}

