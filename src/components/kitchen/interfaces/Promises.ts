// Interface automatically generated by schemas-to-ts

import { Icon } from '../../media/interfaces/Icon';
import { Titles } from '../../common/interfaces/Titles';
import { Icon_Plain } from '../../media/interfaces/Icon';
import { Titles_Plain } from '../../common/interfaces/Titles';
import { Icon_NoRelations } from '../../media/interfaces/Icon';
import { Titles_NoRelations } from '../../common/interfaces/Titles';

export interface Promises {
  title?: string;
  image?: Icon;
  comment?: string;
  promises: Titles[];
}
export interface Promises_Plain {
  title?: string;
  image?: Icon_Plain;
  comment?: string;
  promises: Titles_Plain[];
}

export interface Promises_NoRelations {
  title?: string;
  image?: Icon_NoRelations;
  comment?: string;
  promises: Titles_NoRelations[];
}

