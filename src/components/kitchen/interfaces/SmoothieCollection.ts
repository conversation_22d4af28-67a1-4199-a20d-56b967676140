// Interface automatically generated by schemas-to-ts

import { Icon } from '../../media/interfaces/Icon';
import { Icon_Plain } from '../../media/interfaces/Icon';
import { Icon_NoRelations } from '../../media/interfaces/Icon';

export interface SmoothieCollection {
  title?: string;
  smoothie_collection_items: Icon[];
}
export interface SmoothieCollection_Plain {
  title?: string;
  smoothie_collection_items: Icon_Plain[];
}

export interface SmoothieCollection_NoRelations {
  title?: string;
  smoothie_collection_items: Icon_NoRelations[];
}

