// Interface automatically generated by schemas-to-ts

import { Button } from '../../elements/interfaces/Button';
import { Button_Plain } from '../../elements/interfaces/Button';
import { Button_NoRelations } from '../../elements/interfaces/Button';

export interface Experiment {
  title?: string;
  comment?: string;
  description?: string;
  button?: Button;
}
export interface Experiment_Plain {
  title?: string;
  comment?: string;
  description?: string;
  button?: Button_Plain;
}

export interface Experiment_NoRelations {
  title?: string;
  comment?: string;
  description?: string;
  button?: Button_NoRelations;
}

