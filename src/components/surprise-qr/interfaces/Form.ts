// Interface automatically generated by schemas-to-ts

import { Titles } from '../../common/interfaces/Titles';
import { Icon } from '../../media/interfaces/Icon';
import { FormSubmissionItem } from './FormSubmissionItem';
import { Titles_Plain } from '../../common/interfaces/Titles';
import { Icon_Plain } from '../../media/interfaces/Icon';
import { FormSubmissionItem_Plain } from './FormSubmissionItem';
import { Titles_NoRelations } from '../../common/interfaces/Titles';
import { Icon_NoRelations } from '../../media/interfaces/Icon';
import { FormSubmissionItem_NoRelations } from './FormSubmissionItem';

export interface Form {
  question?: string;
  options: Titles[];
  primary_color?: any;
  bg_color?: any;
  side_image?: Icon;
  submit_button_title?: string;
  on_form_submission?: FormSubmissionItem;
}
export interface Form_Plain {
  question?: string;
  options: Titles_Plain[];
  primary_color?: any;
  bg_color?: any;
  side_image?: Icon_Plain;
  submit_button_title?: string;
  on_form_submission?: FormSubmissionItem_Plain;
}

export interface Form_NoRelations {
  question?: string;
  options: Titles_NoRelations[];
  primary_color?: any;
  bg_color?: any;
  side_image?: Icon_NoRelations;
  submit_button_title?: string;
  on_form_submission?: FormSubmissionItem_NoRelations;
}

