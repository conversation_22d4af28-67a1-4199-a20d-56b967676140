// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface OpenGraph {
  ogTitle: string;
  ogDescription: string;
  ogImage?: { data: Media };
  ogUrl?: string;
  ogType?: string;
}
export interface OpenGraph_Plain {
  ogTitle: string;
  ogDescription: string;
  ogImage?: Media_Plain;
  ogUrl?: string;
  ogType?: string;
}

export interface OpenGraph_NoRelations {
  ogTitle: string;
  ogDescription: string;
  ogImage?: number;
  ogUrl?: string;
  ogType?: string;
}

