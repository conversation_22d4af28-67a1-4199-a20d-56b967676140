// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { OpenGraph } from './OpenGraph';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';
import { OpenGraph_Plain } from './OpenGraph';
import { OpenGraph_NoRelations } from './OpenGraph';

export interface Seo {
  metaTitle: string;
  metaDescription: string;
  metaImage?: { data: Media };
  openGraph?: OpenGraph;
  keywords?: string;
  canonicalURL?: string;
  structuredData?: any;
  is_indexed?: boolean;
}
export interface Seo_Plain {
  metaTitle: string;
  metaDescription: string;
  metaImage?: Media_Plain;
  openGraph?: OpenGraph_Plain;
  keywords?: string;
  canonicalURL?: string;
  structuredData?: any;
  is_indexed?: boolean;
}

export interface Seo_NoRelations {
  metaTitle: string;
  metaDescription: string;
  metaImage?: number;
  openGraph?: OpenGraph_NoRelations;
  keywords?: string;
  canonicalURL?: string;
  structuredData?: any;
  is_indexed?: boolean;
}

