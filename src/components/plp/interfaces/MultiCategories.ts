// Interface automatically generated by schemas-to-ts

import { MultiCategoryItems } from './MultiCategoryItems';
import { MultiCategoryItems_Plain } from './MultiCategoryItems';
import { MultiCategoryItems_NoRelations } from './MultiCategoryItems';

export interface MultiCategories {
  title?: string;
  subtitle?: string;
  multi_category_items: MultiCategoryItems[];
  show_products_below?: boolean;
}
export interface MultiCategories_Plain {
  title?: string;
  subtitle?: string;
  multi_category_items: MultiCategoryItems_Plain[];
  show_products_below?: boolean;
}

export interface MultiCategories_NoRelations {
  title?: string;
  subtitle?: string;
  multi_category_items: MultiCategoryItems_NoRelations[];
  show_products_below?: boolean;
}

